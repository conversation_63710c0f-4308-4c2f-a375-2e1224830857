'use client';

import { useChat } from '@/hooks/useChat';
import Sidebar from '@/components/Sidebar';
import ChatArea from '@/components/ChatArea';
import ChatInput from '@/components/ChatInput';

export default function Home() {
  const {
    conversations,
    currentConversation,
    currentConversationId,
    isLoading,
    error,
    sidebarOpen,
    createNewConversation,
    selectConversation,
    deleteConversation,
    renameConversation,
    sendMessage,
    toggleSidebar,
    clearError
  } = useChat();

  const handleNewChat = () => {
    createNewConversation();
  };

  const handleSendMessage = (message: string) => {
    sendMessage(message);
  };

  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar
        conversations={conversations}
        currentConversationId={currentConversationId}
        isOpen={sidebarOpen}
        onToggle={toggleSidebar}
        onNewChat={handleNewChat}
        onSelectConversation={selectConversation}
        onDeleteConversation={deleteConversation}
        onRenameConversation={renameConversation}
      />

      <div className="flex-1 flex flex-col">
        <ChatArea
          messages={currentConversation?.messages || []}
          isLoading={isLoading}
          error={error}
        />

        <ChatInput
          onSendMessage={handleSendMessage}
          disabled={isLoading}
          placeholder={
            !process.env.NEXT_PUBLIC_CHUTES_API_TOKEN
              ? "Please set NEXT_PUBLIC_CHUTES_API_TOKEN environment variable..."
              : "Type your message..."
          }
        />
      </div>
    </div>
  );
}
