# Nexus - AI Chat Assistant

A ChatGPT-like AI chat application built with Next.js, featuring a modern UI with sidebar navigation and local conversation storage.

## Features

- 🤖 **AI-Powered Chat**: Integrates with OpenAI-compatible APIs (using Chutes AI as example)
- 💬 **Multiple Conversations**: Create, manage, and switch between multiple chat sessions
- 💾 **Local Storage**: All conversations are saved locally in your browser
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile devices
- 🎨 **Modern UI**: Clean, ChatGPT-inspired interface with dark sidebar
- ⚡ **Real-time Streaming**: Messages stream in real-time as they're generated
- ✏️ **Conversation Management**: Rename, delete, and organize your chats

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd nexus-chat-app
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

4. Edit `.env.local` and add your API token:
```env
NEXT_PUBLIC_CHUTES_API_TOKEN=your_chutes_api_token_here
```

5. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## API Configuration

This app is configured to work with Chutes AI, but can be adapted for any OpenAI-compatible API:

- **Endpoint**: `https://llm.chutes.ai/v1/chat/completions`
- **Model**: `moonshotai/Kimi-K2-Instruct`
- **Authentication**: Bearer token via `NEXT_PUBLIC_CHUTES_API_TOKEN`

To use a different API, modify the `APIService` class in `src/lib/api.ts`.

## Project Structure

```
src/
├── app/                 # Next.js app directory
├── components/          # React components
│   ├── Sidebar.tsx     # Navigation sidebar
│   ├── ChatArea.tsx    # Main chat display
│   ├── ChatInput.tsx   # Message input component
│   └── MessageBubble.tsx # Individual message display
├── hooks/              # Custom React hooks
│   └── useChat.ts      # Main chat state management
├── lib/                # Utility libraries
│   ├── api.ts          # API service for LLM communication
│   ├── storage.ts      # Local storage utilities
│   └── utils.ts        # General utilities
└── types/              # TypeScript type definitions
    └── chat.ts         # Chat-related types
```

## Usage

1. **Start a New Chat**: Click the "New Chat" button in the sidebar
2. **Send Messages**: Type your message and press Enter (Shift+Enter for new lines)
3. **Manage Conversations**:
   - Click on any conversation in the sidebar to switch to it
   - Hover over conversations to see rename and delete options
   - Conversations are automatically titled based on the first message
4. **Mobile**: Use the hamburger menu to access the sidebar on mobile devices

## Technologies Used

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety and better development experience
- **Tailwind CSS** - Utility-first CSS framework
- **Heroicons** - Beautiful SVG icons
- **Local Storage** - Browser-based conversation persistence

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
