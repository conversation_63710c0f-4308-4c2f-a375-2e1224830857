import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Stream test API called with:', body);

    const apiKey = process.env.NEXT_PUBLIC_CHUTES_API_TOKEN;
    
    if (!apiKey) {
      return NextResponse.json({ error: 'API key not found' }, { status: 500 });
    }

    // Test the streaming API
    const response = await fetch('https://llm.chutes.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'moonshotai/Kimi-K2-Instruct',
        messages: [{ role: 'user', content: 'Say hello in a few words' }],
        stream: true,
        max_tokens: 50,
        temperature: 0.7
      })
    });

    console.log('Streaming API Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('Streaming API Error:', errorText);
      return NextResponse.json({ 
        error: `API request failed: ${response.status} - ${errorText}` 
      }, { status: response.status });
    }

    if (!response.body) {
      return NextResponse.json({ error: 'No response body' }, { status: 500 });
    }

    // Log the streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let chunks = [];

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        const chunk = decoder.decode(value, { stream: true });
        chunks.push(chunk);
        console.log('Received chunk:', JSON.stringify(chunk));
      }
    } finally {
      reader.releaseLock();
    }

    return NextResponse.json({ 
      success: true, 
      chunks: chunks,
      totalChunks: chunks.length
    });

  } catch (error) {
    console.error('Stream test API error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}
