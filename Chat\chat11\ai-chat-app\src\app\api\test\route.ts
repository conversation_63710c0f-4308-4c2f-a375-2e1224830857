import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Test API called with:', body);

    const apiKey = process.env.NEXT_PUBLIC_CHUTES_API_TOKEN;
    console.log('API Key available:', !!apiKey);

    if (!apiKey) {
      return NextResponse.json({ error: 'API key not found' }, { status: 500 });
    }

    // Test the actual API
    const response = await fetch('https://llm.chutes.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'moonshotai/Kimi-K2-Instruct',
        messages: [{ role: 'user', content: 'Hello, just testing!' }],
        stream: false,
        max_tokens: 50,
        temperature: 0.7
      })
    });

    console.log('API Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('API Error:', errorText);
      return NextResponse.json({ 
        error: `API request failed: ${response.status} - ${errorText}` 
      }, { status: response.status });
    }

    const data = await response.json();
    console.log('API Response:', data);

    return NextResponse.json({ 
      success: true, 
      response: data.choices[0]?.message?.content || 'No content received'
    });

  } catch (error) {
    console.error('Test API error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}
